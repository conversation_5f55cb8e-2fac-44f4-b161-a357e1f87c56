"""
FastAPI服务入口
Motion Agent Backend API Server with Taskiq Integration and MongoDB
"""

import sys
from typing import Any

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger
from pydantic import BaseModel

# Import configuration
from .config import get_config, validate_config
# Import unified routers
from .routers import ROUTERS
from .database import init_database, close_database, create_indexes
from .tasks import init_taskiq, shutdown_taskiq

# 获取配置
config = get_config()

# Import animation router (disabled due to missing dependencies)
# try:
#     from backend.animation import animation_router
# except ImportError:
#     # Fallback for relative imports when running from backend directory
#     from animation import animation_router


# Simple models for basic functionality
class MotionRequest(BaseModel):
    text: str
    character_id: str = "default"
    context: dict[str, Any] | None = None


class MotionResponse(BaseModel):
    success: bool
    message: str
    action_sequence: dict[str, Any] | None = None
    error_message: str | None = None


# Configure loguru using config
def setup_logging():
    """设置日志配置"""
    logger.remove()  # Remove default handler

    # 控制台日志
    logger.add(
        sys.stdout,
        format=config.logging.log_format_console,
        level=config.logging.log_level,
    )

    # 文件日志
    log_file_path = f"{config.logging.log_dir}/{config.logging.log_file}"
    logger.add(
        log_file_path,
        rotation=config.logging.log_rotation,
        retention=config.logging.log_retention,
        compression=config.logging.log_compression,
        format=config.logging.log_format_file,
        level=config.logging.log_level,
    )

# 设置日志
setup_logging()

# 验证配置
if not validate_config():
    logger.error("Configuration validation failed, exiting...")
    sys.exit(1)

app = FastAPI(
    title=config.app_name,
    description=config.app_description,
    version=config.app_version,
    debug=config.development.debug,
    docs_url="/docs" if config.development.enable_swagger_ui else None,
    redoc_url="/redoc" if config.development.enable_redoc else None,
)

# CORS middleware using config
app.add_middleware(
    CORSMiddleware,
    allow_origins=config.api.cors_origins,
    allow_credentials=config.api.cors_allow_credentials,
    allow_methods=config.api.cors_allow_methods,
    allow_headers=config.api.cors_allow_headers,
)

# Include all routers
for router in ROUTERS:
    app.include_router(router)

# Initialize NLU pipelines (disabled for basic functionality)
nlu_pipeline = None
langgraph_pipeline = None


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    global nlu_pipeline, langgraph_pipeline
    logger.info("Starting Motion Agent API server with Taskiq...")

    # Initialize database
    logger.info("Initializing MongoDB database...")
    db_success = await init_database()
    if not db_success:
        logger.error("Failed to initialize MongoDB database")
        # 可以选择继续运行或退出
    else:
        # 创建索引
        logger.info("Creating database indexes...")
        await create_indexes()

    # Initialize taskiq
    logger.info("Initializing taskiq...")
    taskiq_success = await init_taskiq()
    if not taskiq_success:
        logger.error("Failed to initialize taskiq")
        # 可以选择继续运行或退出

    # Initialize both pipelines (disabled for basic functionality)
    # nlu_pipeline = NLUPipeline()
    # langgraph_pipeline = LangGraphNLUPipeline()

    logger.success("Motion Agent API with Taskiq initialized successfully")
    logger.info("Motion Agent API is ready to serve requests")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("Shutting down Motion Agent API...")

    # Shutdown taskiq
    await shutdown_taskiq()

    # Close database connections
    await close_database()

    logger.success("Motion Agent API shutdown completed")


# 基础数据模型保留用于向后兼容
class TextInput(BaseModel):
    text: str
    character_id: str = "default"


class AdvancedTextInput(BaseModel):
    text: str
    character_id: str = "default"
    use_langgraph: bool = True
    context: dict[str, Any] | None = None


# 包含专业动画师路由 (disabled due to missing dependencies)
# app.include_router(animation_router)


@app.get("/")
async def root():
    """Health check endpoint"""
    logger.info("Root endpoint accessed")
    return {
        "message": "Professional Motion Agent API is running",
        "version": "2.0.0",
        "features": [
            "Professional Game Animator",
            "Natural Language to Animation",
            "Junior & Intermediate Animator Functions",
            "Blender Integration",
            "FBX Export",
        ],
    }


# 动作生成端点已移至 /motion 路由器
# Motion generation endpoints moved to /motion router


# 动画端点已移至 /animation 路由器
# Animation endpoints moved to /animation router


# 动画和健康检查端点已移至相应的路由器
# Animation and health check endpoints moved to respective routers


if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host=config.api.api_host,
        port=config.api.api_port,
        reload=config.api.api_reload,
        workers=config.api.api_workers if not config.api.api_reload else 1
    )
