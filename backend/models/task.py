"""
任务模型
Task Models
"""

from datetime import datetime
from enum import Enum
from typing import Any, Optional

from beanie import Document, PydanticObjectId
from pydantic import BaseModel, Field
from pymongo import IndexModel


class TaskType(str, Enum):
    """任务类型枚举"""
    ANIMATION_GENERATION = "animation_generation"
    NLU_PROCESSING = "nlu_processing"
    MOTION_CAPTURE = "motion_capture"
    BLENDER_EXPORT = "blender_export"
    CONVERSATION_PROCESSING = "conversation_processing"
    SYSTEM_MAINTENANCE = "system_maintenance"


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    QUEUED = "queued"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class TaskPriority(str, Enum):
    """任务优先级枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Task(Document):
    """任务 MongoDB 文档模型"""

    # 任务标识
    task_id: str = Field(..., description="taskiq 任务ID", unique=True)

    # 任务基本信息
    task_type: TaskType = Field(..., description="任务类型")
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="任务优先级")

    # 状态信息
    status: TaskStatus = Field(default=TaskStatus.PENDING, description="任务状态")
    progress: float = Field(default=0.0, ge=0, le=100, description="进度百分比 0-100")

    # 关联信息
    conversation_id: Optional[PydanticObjectId] = Field(None, description="对话ID")
    message_id: Optional[PydanticObjectId] = Field(None, description="消息ID")
    user_id: Optional[str] = Field(None, description="用户ID")

    # 任务参数和结果
    input_data: dict[str, Any] = Field(default_factory=dict, description="输入数据")
    output_data: dict[str, Any] = Field(default_factory=dict, description="输出数据")
    metadata: dict[str, Any] = Field(default_factory=dict, description="元数据")

    # 执行信息
    worker_id: Optional[str] = Field(None, description="工作节点ID")
    queue_name: Optional[str] = Field(None, description="队列名称")
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")

    # 时间信息
    created_at: datetime = Field(default_factory=datetime.utcnow, description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    estimated_duration: Optional[int] = Field(None, ge=1, description="预估时长（秒）")
    actual_duration: Optional[int] = Field(None, description="实际时长（秒）")

    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[dict[str, Any]] = Field(None, description="错误详情")

    # 标记
    is_cancelled: bool = Field(default=False, description="是否已取消")
    is_system_task: bool = Field(default=False, description="是否系统任务")

    class Settings:
        name = "tasks"
        indexes = [
            IndexModel([("task_id", 1)], unique=True),
            IndexModel([("conversation_id", 1)]),
            IndexModel([("status", 1), ("created_at", -1)]),
            IndexModel([("task_type", 1)]),
            IndexModel([("user_id", 1)]),
            IndexModel([("priority", 1), ("created_at", 1)]),
            IndexModel([("queue_name", 1)]),
            IndexModel([("is_system_task", 1)]),
        ]

    def start_task(self, worker_id: Optional[str] = None):
        """开始任务"""
        self.status = TaskStatus.RUNNING
        self.started_at = datetime.utcnow()
        if worker_id:
            self.worker_id = worker_id

    def complete_task(self, output_data: Optional[dict[str, Any]] = None):
        """完成任务"""
        self.status = TaskStatus.COMPLETED
        self.progress = 100.0
        self.completed_at = datetime.utcnow()

        if output_data:
            self.output_data.update(output_data)

        # 计算实际执行时间
        if self.started_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            self.actual_duration = int(duration)

    def fail_task(self, error_message: str, error_code: Optional[str] = None, error_details: Optional[dict[str, Any]] = None):
        """任务失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.error_message = error_message
        self.error_code = error_code
        if error_details:
            self.error_details = error_details

        # 计算实际执行时间
        if self.started_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            self.actual_duration = int(duration)

    def cancel_task(self):
        """取消任务"""
        self.status = TaskStatus.CANCELLED
        self.is_cancelled = True
        self.completed_at = datetime.utcnow()

        # 计算实际执行时间
        if self.started_at:
            duration = (self.completed_at - self.started_at).total_seconds()
            self.actual_duration = int(duration)

    def update_progress(self, progress: float):
        """更新进度"""
        self.progress = max(0.0, min(100.0, progress))


class TaskCreate(BaseModel):
    """创建任务请求模型"""
    task_type: TaskType = Field(..., description="任务类型")
    task_name: str = Field(..., min_length=1, max_length=255, description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: TaskPriority = Field(default=TaskPriority.NORMAL, description="任务优先级")
    
    # 关联信息
    conversation_id: Optional[str] = Field(None, description="对话ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    
    # 任务参数
    input_data: dict[str, Any] = Field(default_factory=dict, description="输入数据")
    metadata: dict[str, Any] = Field(default_factory=dict, description="元数据")
    
    # 执行配置
    queue_name: Optional[str] = Field(None, description="队列名称")
    max_retries: int = Field(default=3, ge=0, le=10, description="最大重试次数")
    estimated_duration: Optional[int] = Field(None, ge=1, description="预估时长（秒）")


class TaskUpdate(BaseModel):
    """更新任务请求模型"""
    status: Optional[TaskStatus] = Field(None, description="任务状态")
    progress: Optional[float] = Field(None, ge=0, le=100, description="进度百分比")
    output_data: Optional[dict[str, Any]] = Field(None, description="输出数据")
    metadata: Optional[dict[str, Any]] = Field(None, description="元数据")
    worker_id: Optional[str] = Field(None, description="工作节点ID")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    actual_duration: Optional[int] = Field(None, description="实际时长（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[dict[str, Any]] = Field(None, description="错误详情")


class TaskResponse(BaseModel):
    """任务响应模型"""
    id: str = Field(..., description="任务数据库ID")
    task_id: str = Field(..., description="任务ID")
    
    # 任务基本信息
    task_type: TaskType = Field(..., description="任务类型")
    task_name: str = Field(..., description="任务名称")
    description: Optional[str] = Field(None, description="任务描述")
    priority: TaskPriority = Field(..., description="任务优先级")
    
    # 状态信息
    status: TaskStatus = Field(..., description="任务状态")
    progress: float = Field(..., description="进度百分比")
    
    # 关联信息
    conversation_id: Optional[str] = Field(None, description="对话ID")
    message_id: Optional[str] = Field(None, description="消息ID")
    user_id: Optional[str] = Field(None, description="用户ID")
    
    # 任务数据
    input_data: dict[str, Any] = Field(..., description="输入数据")
    output_data: dict[str, Any] = Field(..., description="输出数据")
    metadata: dict[str, Any] = Field(..., description="元数据")
    
    # 执行信息
    worker_id: Optional[str] = Field(None, description="工作节点ID")
    queue_name: Optional[str] = Field(None, description="队列名称")
    retry_count: int = Field(..., description="重试次数")
    max_retries: int = Field(..., description="最大重试次数")
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    estimated_duration: Optional[int] = Field(None, description="预估时长（秒）")
    actual_duration: Optional[int] = Field(None, description="实际时长（秒）")
    
    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    error_details: Optional[dict[str, Any]] = Field(None, description="错误详情")
    
    # 标记
    is_cancelled: bool = Field(..., description="是否已取消")
    is_system_task: bool = Field(..., description="是否系统任务")

    model_config = {"from_attributes": True}


class TaskList(BaseModel):
    """任务列表响应模型"""
    tasks: list[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class TaskFilter(BaseModel):
    """任务过滤条件"""
    task_type: Optional[TaskType] = Field(None, description="任务类型过滤")
    status: Optional[TaskStatus] = Field(None, description="状态过滤")
    priority: Optional[TaskPriority] = Field(None, description="优先级过滤")
    conversation_id: Optional[str] = Field(None, description="对话ID过滤")
    user_id: Optional[str] = Field(None, description="用户ID过滤")
    is_system_task: Optional[bool] = Field(None, description="是否系统任务过滤")
    
    # 分页参数
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=20, ge=1, le=100, description="每页大小")
    
    # 排序参数
    sort_by: str = Field(default="created_at", description="排序字段")
    sort_order: str = Field(default="desc", pattern="^(asc|desc)$", description="排序方向")


class TaskStats(BaseModel):
    """任务统计模型"""
    total_tasks: int = Field(..., description="总任务数")
    pending_tasks: int = Field(..., description="待处理任务数")
    running_tasks: int = Field(..., description="运行中任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    cancelled_tasks: int = Field(..., description="已取消任务数")
    average_duration: Optional[float] = Field(None, description="平均执行时长（秒）")
    success_rate: float = Field(..., description="成功率（百分比）")
