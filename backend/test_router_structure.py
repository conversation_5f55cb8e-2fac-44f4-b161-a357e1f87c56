#!/usr/bin/env python3
"""
测试路由器结构
Test Router Structure

验证所有路由器文件是否正确创建和配置
Verify that all router files are correctly created and configured
"""

import os
import re
from pathlib import Path


def test_router_files():
    """测试路由器文件是否存在"""
    router_dir = Path("routers")
    expected_files = [
        "animation.py",
        "conversations.py", 
        "health.py",
        "motion.py",
        "tasks.py",
        "__init__.py"
    ]
    
    print("🔍 检查路由器文件...")
    for file in expected_files:
        file_path = router_dir / file
        if file_path.exists():
            print(f"✅ {file} - 存在")
        else:
            print(f"❌ {file} - 缺失")
    
    return all((router_dir / file).exists() for file in expected_files)


def test_router_content():
    """测试路由器内容结构"""
    router_dir = Path("routers")
    
    print("\n🔍 检查路由器内容结构...")
    
    # 检查每个路由器文件的基本结构
    router_files = {
        "animation.py": {
            "prefix": "/animation",
            "tags": "Animation",
            "endpoints": ["presets", "examples", "generate", "download", "health"]
        },
        "conversations.py": {
            "prefix": "/conversations", 
            "tags": "Conversations",
            "endpoints": ["POST /", "GET /", "PUT /{conversation_id}", "DELETE /{conversation_id}"]
        },
        "health.py": {
            "prefix": "/health",
            "tags": "Health", 
            "endpoints": ["GET /", "check", "services"]
        },
        "motion.py": {
            "prefix": "/motion",
            "tags": "Motion",
            "endpoints": ["generate", "generate-advanced"]
        },
        "tasks.py": {
            "prefix": "/tasks",
            "tags": "Tasks",
            "endpoints": ["POST /", "GET /", "PUT /{task_id}", "cancel"]
        }
    }
    
    for filename, expected in router_files.items():
        file_path = router_dir / filename
        if not file_path.exists():
            print(f"❌ {filename} - 文件不存在")
            continue
            
        content = file_path.read_text()
        
        # 检查基本结构
        has_router = "router = APIRouter" in content
        has_prefix = expected["prefix"] in content
        has_tags = expected["tags"] in content
        
        print(f"\n📁 {filename}:")
        print(f"  ✅ APIRouter定义: {has_router}")
        print(f"  ✅ 前缀设置: {has_prefix}")
        print(f"  ✅ 标签设置: {has_tags}")
        
        # 检查端点
        endpoint_count = len(re.findall(r'@router\.(get|post|put|delete)', content))
        print(f"  📊 端点数量: {endpoint_count}")


def test_init_file():
    """测试__init__.py文件"""
    init_file = Path("routers/__init__.py")
    
    print("\n🔍 检查__init__.py文件...")
    
    if not init_file.exists():
        print("❌ __init__.py 文件不存在")
        return False
        
    content = init_file.read_text()
    
    # 检查导入
    expected_imports = [
        "animation_router",
        "conversations_router", 
        "health_router",
        "motion_router",
        "tasks_router"
    ]
    
    for import_name in expected_imports:
        if import_name in content:
            print(f"✅ {import_name} - 已导入")
        else:
            print(f"❌ {import_name} - 未导入")
    
    # 检查ROUTERS列表
    has_routers_list = "ROUTERS = [" in content
    print(f"✅ ROUTERS列表定义: {has_routers_list}")
    
    return True


def main():
    """主测试函数"""
    print("🚀 Motion Agent API 路由器结构测试")
    print("=" * 50)
    
    # 测试文件存在性
    files_ok = test_router_files()
    
    # 测试内容结构
    test_router_content()
    
    # 测试__init__.py
    init_ok = test_init_file()
    
    print("\n" + "=" * 50)
    if files_ok and init_ok:
        print("🎉 所有路由器结构测试通过！")
        print("\n📋 API路由器总结:")
        print("  • /health - 健康检查")
        print("  • /motion - 动作生成") 
        print("  • /animation - 动画生成")
        print("  • /conversations - 对话管理")
        print("  • /tasks - 任务管理")
        print("\n✨ 路由器已统一管理，可以通过 ROUTERS 列表批量注册")
    else:
        print("❌ 部分测试失败，请检查路由器配置")


if __name__ == "__main__":
    main()
