"""
配置管理系统
Configuration Management System
"""

import os
from typing import Any, Dict, List, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from loguru import logger


class DatabaseConfig(BaseSettings):
    """数据库配置"""
    
    # MongoDB 配置
    mongodb_url: str = Field(default="mongodb://localhost:27017", env="MONGODB_URL")
    mongodb_database: str = Field(default="motion_agent", env="MONGODB_DATABASE")
    mongodb_username: Optional[str] = Field(default=None, env="MONGODB_USERNAME")
    mongodb_password: Optional[str] = Field(default=None, env="MONGODB_PASSWORD")
    mongodb_auth_source: str = Field(default="admin", env="MONGODB_AUTH_SOURCE")
    
    # 连接池配置
    mongodb_max_connections: int = Field(default=100, env="MONGODB_MAX_CONNECTIONS")
    mongodb_min_connections: int = Field(default=10, env="MONGODB_MIN_CONNECTIONS")
    mongodb_max_idle_time: int = Field(default=30000, env="MONGODB_MAX_IDLE_TIME")  # 毫秒
    mongodb_connect_timeout: int = Field(default=10000, env="MONGODB_CONNECT_TIMEOUT")  # 毫秒
    mongodb_server_selection_timeout: int = Field(default=5000, env="MONGODB_SERVER_SELECTION_TIMEOUT")  # 毫秒
    
    @property
    def mongodb_connection_string(self) -> str:
        """构建 MongoDB 连接字符串"""
        if self.mongodb_username and self.mongodb_password:
            # 带认证的连接字符串
            base_url = self.mongodb_url.replace("mongodb://", "")
            return f"mongodb://{self.mongodb_username}:{self.mongodb_password}@{base_url}/{self.mongodb_database}?authSource={self.mongodb_auth_source}"
        else:
            # 无认证的连接字符串
            return f"{self.mongodb_url}/{self.mongodb_database}"


class RedisConfig(BaseSettings):
    """Redis 配置"""
    
    # Redis 基础配置
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT")
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_db: int = Field(default=0, env="REDIS_DB")
    
    # Redis 结果后端配置
    redis_result_host: str = Field(default="localhost", env="REDIS_RESULT_HOST")
    redis_result_port: int = Field(default=6379, env="REDIS_RESULT_PORT")
    redis_result_password: Optional[str] = Field(default=None, env="REDIS_RESULT_PASSWORD")
    redis_result_db: int = Field(default=1, env="REDIS_RESULT_DB")
    
    # 连接池配置
    redis_max_connections: int = Field(default=50, env="REDIS_MAX_CONNECTIONS")
    redis_socket_timeout: int = Field(default=5, env="REDIS_SOCKET_TIMEOUT")  # 秒
    redis_socket_connect_timeout: int = Field(default=5, env="REDIS_SOCKET_CONNECT_TIMEOUT")  # 秒
    
    @property
    def redis_url(self) -> str:
        """构建 Redis URL"""
        auth = f":{self.redis_password}@" if self.redis_password else ""
        return f"redis://{auth}{self.redis_host}:{self.redis_port}/{self.redis_db}"
    
    @property
    def redis_result_url(self) -> str:
        """构建 Redis 结果后端 URL"""
        auth = f":{self.redis_result_password}@" if self.redis_result_password else ""
        return f"redis://{auth}{self.redis_result_host}:{self.redis_result_port}/{self.redis_result_db}"


class APIConfig(BaseSettings):
    """API 服务配置"""
    
    # 服务器配置
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=9000, env="API_PORT")
    api_reload: bool = Field(default=True, env="API_RELOAD")
    api_workers: int = Field(default=1, env="API_WORKERS")
    
    # CORS 配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(default=True, env="CORS_ALLOW_CREDENTIALS")
    cors_allow_methods: List[str] = Field(default=["*"], env="CORS_ALLOW_METHODS")
    cors_allow_headers: List[str] = Field(default=["*"], env="CORS_ALLOW_HEADERS")
    
    # API 限制配置
    api_rate_limit: str = Field(default="100/minute", env="API_RATE_LIMIT")
    api_max_request_size: int = Field(default=16 * 1024 * 1024, env="API_MAX_REQUEST_SIZE")  # 16MB
    
    @field_validator('cors_origins', mode='before')
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(',')]
        return v

    @field_validator('cors_allow_methods', mode='before')
    @classmethod
    def parse_cors_methods(cls, v):
        if isinstance(v, str):
            return [method.strip() for method in v.split(',')]
        return v

    @field_validator('cors_allow_headers', mode='before')
    @classmethod
    def parse_cors_headers(cls, v):
        if isinstance(v, str):
            return [header.strip() for header in v.split(',')]
        return v


class TaskiqConfig(BaseSettings):
    """Taskiq 任务队列配置"""
    
    # Worker 配置
    taskiq_worker_concurrency: int = Field(default=4, env="TASKIQ_WORKER_CONCURRENCY")
    taskiq_max_retries: int = Field(default=3, env="TASKIQ_MAX_RETRIES")
    taskiq_retry_delay: int = Field(default=60, env="TASKIQ_RETRY_DELAY")  # 秒
    
    # 队列配置
    taskiq_default_queue: str = Field(default="default", env="TASKIQ_DEFAULT_QUEUE")
    taskiq_animation_queue: str = Field(default="animation", env="TASKIQ_ANIMATION_QUEUE")
    taskiq_conversation_queue: str = Field(default="conversation", env="TASKIQ_CONVERSATION_QUEUE")
    taskiq_nlu_queue: str = Field(default="nlu", env="TASKIQ_NLU_QUEUE")
    taskiq_system_queue: str = Field(default="system", env="TASKIQ_SYSTEM_QUEUE")
    
    # 任务超时配置
    taskiq_default_timeout: int = Field(default=300, env="TASKIQ_DEFAULT_TIMEOUT")  # 5分钟
    taskiq_animation_timeout: int = Field(default=600, env="TASKIQ_ANIMATION_TIMEOUT")  # 10分钟
    taskiq_conversation_timeout: int = Field(default=180, env="TASKIQ_CONVERSATION_TIMEOUT")  # 3分钟
    taskiq_nlu_timeout: int = Field(default=60, env="TASKIQ_NLU_TIMEOUT")  # 1分钟


class BlenderConfig(BaseSettings):
    """Blender 配置"""
    
    # Blender 路径配置
    blender_path: str = Field(default="/Applications/Blender.app/Contents/MacOS/Blender", env="BLENDER_PATH")
    blender_scripts_path: str = Field(default="./blender_scripts", env="BLENDER_SCRIPTS_PATH")
    
    # 角色模型配置
    default_character_model: str = Field(default="models/default_character.blend", env="DEFAULT_CHARACTER_MODEL")
    character_models_dir: str = Field(default="models/characters/", env="CHARACTER_MODELS_DIR")
    
    # 动画设置
    default_frame_rate: int = Field(default=30, env="DEFAULT_FRAME_RATE")
    default_animation_quality: str = Field(default="game_ready", env="DEFAULT_ANIMATION_QUALITY")
    default_export_format: str = Field(default="fbx", env="DEFAULT_EXPORT_FORMAT")
    
    # 输出配置
    animation_output_dir: str = Field(default="./output/animations", env="ANIMATION_OUTPUT_DIR")
    animation_temp_dir: str = Field(default="./temp/animation_data", env="ANIMATION_TEMP_DIR")
    
    # 性能配置
    max_animation_duration: float = Field(default=30.0, env="MAX_ANIMATION_DURATION")
    max_actions_per_sequence: int = Field(default=15, env="MAX_ACTIONS_PER_SEQUENCE")
    blender_timeout: int = Field(default=300, env="BLENDER_TIMEOUT")  # 秒


class LoggingConfig(BaseSettings):
    """日志配置"""
    
    # 基础日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_dir: str = Field(default="logs", env="LOG_DIR")
    log_file: str = Field(default="motion_agent.log", env="LOG_FILE")
    
    # 日志轮转配置
    log_rotation: str = Field(default="10 MB", env="LOG_ROTATION")
    log_retention: str = Field(default="7 days", env="LOG_RETENTION")
    log_compression: str = Field(default="gz", env="LOG_COMPRESSION")
    
    # 日志格式配置
    log_format_console: str = Field(
        default="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        env="LOG_FORMAT_CONSOLE"
    )
    log_format_file: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        env="LOG_FORMAT_FILE"
    )
    
    # 特殊日志配置
    enable_access_log: bool = Field(default=True, env="ENABLE_ACCESS_LOG")
    enable_error_log: bool = Field(default=True, env="ENABLE_ERROR_LOG")
    enable_task_log: bool = Field(default=True, env="ENABLE_TASK_LOG")


class SecurityConfig(BaseSettings):
    """安全配置"""
    
    # 基础安全配置
    secret_key: str = Field(default="your-secret-key-change-in-production", env="SECRET_KEY")
    allowed_hosts: List[str] = Field(default=["localhost", "127.0.0.1"], env="ALLOWED_HOSTS")
    
    # API 安全配置
    enable_api_key_auth: bool = Field(default=False, env="ENABLE_API_KEY_AUTH")
    api_key_header: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    valid_api_keys: List[str] = Field(default=[], env="VALID_API_KEYS")
    
    # 会话配置
    session_timeout: int = Field(default=3600, env="SESSION_TIMEOUT")  # 1小时
    max_sessions_per_user: int = Field(default=5, env="MAX_SESSIONS_PER_USER")
    
    @field_validator('allowed_hosts', mode='before')
    @classmethod
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v

    @field_validator('valid_api_keys', mode='before')
    @classmethod
    def parse_api_keys(cls, v):
        if isinstance(v, str):
            return [key.strip() for key in v.split(',')]
        return v


class DevelopmentConfig(BaseSettings):
    """开发配置"""
    
    # 开发模式配置
    debug: bool = Field(default=True, env="DEBUG")
    development_mode: bool = Field(default=True, env="DEVELOPMENT_MODE")
    
    # 开发工具配置
    enable_profiling: bool = Field(default=False, env="ENABLE_PROFILING")
    enable_metrics: bool = Field(default=True, env="ENABLE_METRICS")
    enable_swagger_ui: bool = Field(default=True, env="ENABLE_SWAGGER_UI")
    enable_redoc: bool = Field(default=True, env="ENABLE_REDOC")
    
    # 测试配置
    enable_test_endpoints: bool = Field(default=True, env="ENABLE_TEST_ENDPOINTS")
    test_data_cleanup: bool = Field(default=True, env="TEST_DATA_CLEANUP")


class AppConfig(BaseSettings):
    """应用主配置"""
    
    # 应用信息
    app_name: str = Field(default="Motion Agent", env="APP_NAME")
    app_version: str = Field(default="3.0.0", env="APP_VERSION")
    app_description: str = Field(default="Professional Game Animator with Taskiq", env="APP_DESCRIPTION")
    
    # 环境配置
    environment: str = Field(default="development", env="ENVIRONMENT")
    
    # 子配置
    database: DatabaseConfig = DatabaseConfig()
    redis: RedisConfig = RedisConfig()
    api: APIConfig = APIConfig()
    taskiq: TaskiqConfig = TaskiqConfig()
    blender: BlenderConfig = BlenderConfig()
    logging: LoggingConfig = LoggingConfig()
    security: SecurityConfig = SecurityConfig()
    development: DevelopmentConfig = DevelopmentConfig()
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False
    }


# 全局配置实例
config = AppConfig()


def get_config() -> AppConfig:
    """获取配置实例"""
    return config


def reload_config():
    """重新加载配置"""
    global config
    config = AppConfig()
    logger.info("Configuration reloaded")


def validate_config():
    """验证配置"""
    errors = []
    
    # 验证必要的目录
    required_dirs = [
        config.logging.log_dir,
        config.blender.animation_output_dir,
        config.blender.animation_temp_dir,
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"Created directory: {dir_path}")
            except Exception as e:
                errors.append(f"Cannot create directory {dir_path}: {e}")
    
    # 验证 Blender 路径
    if not os.path.exists(config.blender.blender_path):
        errors.append(f"Blender not found at: {config.blender.blender_path}")
    
    # 验证端口范围
    if not (1 <= config.api.api_port <= 65535):
        errors.append(f"Invalid API port: {config.api.api_port}")
    
    if not (1 <= config.redis.redis_port <= 65535):
        errors.append(f"Invalid Redis port: {config.redis.redis_port}")
    
    if errors:
        logger.error("Configuration validation failed:")
        for error in errors:
            logger.error(f"  - {error}")
        return False
    
    logger.success("Configuration validation passed")
    return True


# 导出主要组件
__all__ = [
    "config",
    "get_config",
    "reload_config",
    "validate_config",
    "AppConfig",
    "DatabaseConfig",
    "RedisConfig",
    "APIConfig",
    "TaskiqConfig",
    "BlenderConfig",
    "LoggingConfig",
    "SecurityConfig",
    "DevelopmentConfig",
]
